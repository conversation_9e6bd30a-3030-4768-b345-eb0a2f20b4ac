from app import db
from datetime import datetime

class Image(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    time_mark = db.Column(db.Time)  # Time mark for the image
    ai_content = db.Column(db.Text)  # AI-processed content
    timeline_id = db.Column(db.Integer, db.<PERSON>ey('timeline.id'), nullable=False)
    user_id = db.Column(db.Integer, db.<PERSON><PERSON>ey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<Image {self.original_filename}>'
