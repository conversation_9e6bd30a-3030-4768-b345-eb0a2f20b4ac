from app import db
from datetime import datetime

class Timeline(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    work_date = db.Column(db.Date, nullable=False)
    description = db.Column(db.Text)
    business_content = db.Column(db.Text)  # Markdown content for business description
    evaluation_content = db.Column(db.Text)  # Generated evaluation in markdown
    user_id = db.Column(db.Integer, db.<PERSON>ey('user.id'), nullable=False)
    company_id = db.Column(db.Integer, db.<PERSON>ey('company.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    images = db.relationship('Image', backref='timeline', lazy='dynamic')
    
    def __repr__(self):
        return f'<Timeline {self.title} - {self.work_date}>'
