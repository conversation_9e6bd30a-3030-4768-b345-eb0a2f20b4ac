{% extends "base.html" %}

{% block title %}Timelines - Timeline Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-clock me-2"></i>Timelines</h1>
    <a href="{{ url_for('timeline.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i> New Timeline
    </a>
</div>

<!-- Filters -->
<div class="card mb-4 shadow-sm">
    <div class="card-header bg-light">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Timelines</h5>
    </div>
    <div class="card-body">
        <form method="get" action="{{ url_for('timeline.index') }}" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">Company</label>
                <select name="company_id" class="form-select">
                    <option value="">All Companies</option>
                    {% for company in companies %}
                    <option value="{{ company.id }}" {% if selected_company == company.id %}selected{% endif %}>
                        {{ company.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">Date From</label>
                <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
            </div>
            <div class="col-md-4">
                <label class="form-label">Date To</label>
                <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i> Apply Filters
                </button>
                <a href="{{ url_for('timeline.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-undo me-1"></i> Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Timeline List -->
{% if timelines %}
<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Timeline List</h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Title</th>
                        <th>Work Date</th>
                        <th>Company</th>
                        <th>Images</th>
                        <th>Created By</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for timeline in timelines %}
                    <tr>
                        <td>{{ timeline.title }}</td>
                        <td>{{ timeline.work_date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ timeline.company.name }}</td>
                        <td>{{ timeline.images.count() }}</td>
                        <td>{{ timeline.user.username }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('timeline.view', id=timeline.id) }}" class="btn btn-info" title="View">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.id == timeline.user_id or current_user.is_admin %}
                                <a href="{{ url_for('timeline.edit', id=timeline.id) }}" class="btn btn-warning" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-danger" title="Delete" 
                                        data-bs-toggle="modal" data-bs-target="#deleteModal{{ timeline.id }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                            
                            <!-- Delete Modal -->
                            <div class="modal fade" id="deleteModal{{ timeline.id }}" tabindex="-1">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Confirm Delete</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Are you sure you want to delete the timeline "{{ timeline.title }}"?</p>
                                            <p class="text-danger"><small>This action cannot be undone.</small></p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                            <form action="{{ url_for('timeline.delete', id=timeline.id) }}" method="post">
                                                <button type="submit" class="btn btn-danger">Delete</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% else %}
<div class="alert alert-info">
    <i class="fas fa-info-circle me-2"></i> No timelines found. Create a new timeline to get started.
</div>
{% endif %}
{% endblock %}
