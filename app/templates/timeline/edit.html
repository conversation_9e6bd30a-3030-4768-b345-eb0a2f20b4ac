{% extends "base.html" %}

{% block title %}Edit Timeline - Timeline Manager{% endblock %}

{% block styles %}
<!-- Add Markdown editor CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.css">
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-edit me-2"></i>Edit Timeline</h1>
    <div>
        <a href="{{ url_for('timeline.view', id=timeline.id) }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-eye me-1"></i> View Timeline
        </a>
        <a href="{{ url_for('timeline.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Timelines
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-warning text-dark">
        <h4 class="mb-0">Edit Timeline Details</h4>
    </div>
    <div class="card-body">
        <form method="post" action="{{ url_for('timeline.edit', id=timeline.id) }}">
            {{ form.hidden_tag() }}
            <div class="row mb-3">
                <div class="col-md-6">
                    {{ form.title.label(class="form-label") }}
                    {{ form.title(class="form-control", placeholder="Enter timeline title") }}
                    {% for error in form.title.errors %}
                        <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
                <div class="col-md-6">
                    {{ form.work_date.label(class="form-label") }}
                    {{ form.work_date(class="form-control", type="date") }}
                    {% for error in form.work_date.errors %}
                        <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
            </div>
            <div class="mb-3">
                {{ form.company_id.label(class="form-label") }}
                {{ form.company_id(class="form-select") }}
                {% for error in form.company_id.errors %}
                    <div class="text-danger">{{ error }}</div>
                {% endfor %}
            </div>
            <div class="mb-3">
                {{ form.description.label(class="form-label") }}
                {{ form.description(class="form-control", rows=3, placeholder="Brief description of this timeline") }}
                {% for error in form.description.errors %}
                    <div class="text-danger">{{ error }}</div>
                {% endfor %}
            </div>
            <div class="mb-3">
                {{ form.business_content.label(class="form-label") }}
                <small class="text-muted d-block mb-2">Use Markdown to format your content</small>
                {{ form.business_content(class="form-control", rows=10, id="markdown-editor", placeholder="Detailed business content in Markdown format") }}
                {% for error in form.business_content.errors %}
                    <div class="text-danger">{{ error }}</div>
                {% endfor %}
            </div>
            <div class="d-grid">
                {{ form.submit(class="btn btn-warning btn-lg") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Add Markdown editor JS -->
<script src="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var simplemde = new SimpleMDE({ 
            element: document.getElementById("markdown-editor"),
            spellChecker: false,
            autosave: {
                enabled: true,
                uniqueId: "timeline-edit-{{ timeline.id }}",
                delay: 1000,
            },
        });
    });
</script>
{% endblock %}
