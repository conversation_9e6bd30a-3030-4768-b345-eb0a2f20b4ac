{% extends "base.html" %}

{% block title %}Evaluate Timeline - Timeline Manager{% endblock %}

{% block styles %}
<!-- Add Markdown editor CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.css">
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-chart-line me-2"></i>Evaluate Timeline</h1>
    <div>
        <a href="{{ url_for('timeline.view', id=timeline.id) }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-eye me-1"></i> View Timeline
        </a>
        <a href="{{ url_for('timeline.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Timelines
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-info text-white">
        <h4 class="mb-0">Generate Evaluation for "{{ timeline.title }}"</h4>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i> Enter or update the business content below, then click "Generate Evaluation" to create an AI-powered evaluation of this timeline.
        </div>
        
        <form method="post" action="{{ url_for('timeline.evaluate', id=timeline.id) }}">
            {{ form.hidden_tag() }}
            <div class="mb-4">
                {{ form.business_content.label(class="form-label") }}
                <small class="text-muted d-block mb-2">Use Markdown to format your content</small>
                {{ form.business_content(class="form-control", rows=12, id="markdown-editor", placeholder="Detailed business content in Markdown format") }}
                {% for error in form.business_content.errors %}
                    <div class="text-danger">{{ error }}</div>
                {% endfor %}
            </div>
            <div class="d-grid">
                {{ form.submit(class="btn btn-info btn-lg") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Add Markdown editor JS -->
<script src="https://cdn.jsdelivr.net/simplemde/latest/simplemde.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var simplemde = new SimpleMDE({ 
            element: document.getElementById("markdown-editor"),
            spellChecker: false,
            autosave: {
                enabled: true,
                uniqueId: "timeline-evaluate-{{ timeline.id }}",
                delay: 1000,
            },
        });
    });
</script>
{% endblock %}
