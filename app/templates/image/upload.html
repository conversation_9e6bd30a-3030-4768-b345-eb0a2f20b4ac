{% extends "base.html" %}

{% block title %}Upload Image - Timeline Manager{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-upload me-2"></i>Upload Image</h1>
    <a href="{{ url_for('timeline.view', id=form.timeline_id.data) }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i> Back to Timeline
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Upload New Image</h4>
            </div>
            <div class="card-body">
                <form method="post" action="{{ url_for('image.upload') }}" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    <div class="mb-4">
                        <div class="text-center mb-3">
                            <i class="fas fa-cloud-upload-alt fa-4x text-primary mb-3"></i>
                            <h5>Select an image to upload</h5>
                            <p class="text-muted">Supported formats: JPG, JPEG, PNG, GIF</p>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.image.label(class="form-label") }}
                            {{ form.image(class="form-control", accept="image/*") }}
                            {% for error in form.image.errors %}
                                <div class="text-danger">{{ error }}</div>
                            {% endfor %}
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> After uploading, you'll be able to process the image with AI and add a time mark.
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary btn-lg") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
