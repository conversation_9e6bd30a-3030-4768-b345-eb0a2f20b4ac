/* Custom styles for Timeline Manager */

/* General styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

/* Card styling */
.card {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    border-top-left-radius: 0.5rem !important;
    border-top-right-radius: 0.5rem !important;
    font-weight: 500;
}

/* Navbar styling */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.4rem;
}

/* Button styling */
.btn {
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    font-weight: 500;
}

.btn-primary {
    background-color: #3f51b5;
    border-color: #3f51b5;
}

.btn-primary:hover {
    background-color: #303f9f;
    border-color: #303f9f;
}

.btn-info {
    background-color: #2196f3;
    border-color: #2196f3;
    color: white;
}

.btn-info:hover {
    background-color: #1976d2;
    border-color: #1976d2;
    color: white;
}

.btn-warning {
    background-color: #ff9800;
    border-color: #ff9800;
}

.btn-warning:hover {
    background-color: #f57c00;
    border-color: #f57c00;
}

/* Form styling */
.form-control, .form-select {
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    border: 1px solid #ced4da;
}

.form-control:focus, .form-select:focus {
    border-color: #3f51b5;
    box-shadow: 0 0 0 0.25rem rgba(63, 81, 181, 0.25);
}

/* Table styling */
.table {
    vertical-align: middle;
}

.table th {
    font-weight: 600;
    color: #495057;
}

/* Timeline specific styles */
.markdown-content {
    line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.markdown-content p {
    margin-bottom: 1rem;
}

.markdown-content ul,
.markdown-content ol {
    margin-bottom: 1rem;
    padding-left: 2rem;
}

.markdown-content blockquote {
    padding: 0.5rem 1rem;
    border-left: 4px solid #e9ecef;
    background-color: #f8f9fa;
    margin-bottom: 1rem;
}

/* Horizontal Timeline Styles */
.timeline-container {
    width: 100%;
    overflow-x: auto;
    padding: 20px 0;
    scrollbar-width: thin;
    scrollbar-color: #3f51b5 #f0f0f0;
}

.timeline-container::-webkit-scrollbar {
    height: 8px;
}

.timeline-container::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 4px;
}

.timeline-container::-webkit-scrollbar-thumb {
    background-color: #3f51b5;
    border-radius: 4px;
}

.timeline-track {
    position: relative;
    min-height: 250px;
    padding: 0 10px;
}

.timeline-images {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding-bottom: 40px;
    position: relative;
    z-index: 2;
}

.timeline-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 15px;
    min-width: 150px;
    transition: transform 0.3s ease;
}

.timeline-item:hover {
    transform: translateY(-5px);
}

.timeline-image-container {
    position: relative;
    margin-bottom: 20px;
    border: 3px solid #3f51b5;
    border-radius: 8px;
    overflow: hidden;
    width: 150px;
    height: 150px;
    background-color: #f8f9fa;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.timeline-image-container:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.timeline-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.timeline-image-actions {
    position: absolute;
    bottom: 5px;
    right: 5px;
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.timeline-image-container:hover .timeline-image-actions {
    opacity: 1;
}

.timeline-marker {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
}

.timeline-marker:before {
    content: '';
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 25px;
    background-color: #3f51b5;
}

.timeline-time {
    margin-bottom: 5px;
}

.timeline-label {
    max-width: 150px;
    font-size: 0.8rem;
}

.timeline-line {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 80px;
    height: 3px;
    background-color: #3f51b5;
    z-index: 1;
}

/* Responsive timeline adjustments */
@media (max-width: 768px) {
    .timeline-image-container {
        width: 120px;
        height: 120px;
    }

    .timeline-item {
        min-width: 120px;
        margin: 0 10px;
    }

    .timeline-line {
        bottom: 70px;
    }
}

@media (max-width: 576px) {
    .timeline-image-container {
        width: 100px;
        height: 100px;
    }

    .timeline-item {
        min-width: 100px;
        margin: 0 8px;
    }

    .timeline-line {
        bottom: 60px;
    }
}

/* Image gallery */
.card-img-top {
    height: 120px;
    object-fit: cover;
}

/* Footer */
footer {
    margin-top: 3rem;
    border-top: 1px solid #e9ecef;
}
